import json
import uuid
import logging
from datetime import datetime
from django.utils import timezone

logger = logging.getLogger(__name__)


def get_client_ip(request):
    """
    Get client IP address from request
    """
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0].strip()
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def generate_unique_request_id():
    """
    Generate a unique request ID for logging correlation.
    Format: REQ_<timestamp>_<uuid>
    """
    timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
    unique_suffix = str(uuid.uuid4())[:8]
    return f"REQ_{timestamp}_{unique_suffix}"


def log_request_info(unique_id, request, operation_type="REQUEST"):
    """
    Logs request information in a standardized JSON format with unique id, headers, data, and meta.
    Output format: INFO | <operation_type> | <unique_id> | <headers> | <data>

    Args:
        unique_id (str): Unique identifier for the request
        request: Django request object
        operation_type (str): Type of operation (REQUEST, RESPONSE, ERROR, etc.)
    """
    try:
        # Extract headers safely
        headers = {}
        if hasattr(request, "headers"):
            headers = dict(request.headers)
        elif hasattr(request, "META"):
            # Extract HTTP headers from META
            headers = {k: v for k, v in request.META.items() if k.startswith("HTTP_")}

        # Extract request data safely
        data = {}
        if hasattr(request, "data"):
            data = request.data
        elif hasattr(request, "POST"):
            data = dict(request.POST)

        # Create log entry
        log_data = {
            "unique_id": unique_id,
            "timestamp": timezone.now().isoformat(),
            "method": getattr(request, "method", "UNKNOWN"),
            "path": getattr(request, "path", ""),
            "headers": headers,
            "data": data,
            "client_ip": get_client_ip(request),
            "user_agent": (
                request.META.get("HTTP_USER_AGENT", "")
                if hasattr(request, "META")
                else ""
            ),
        }

        # Convert to JSON string
        headers_json = json.dumps(headers, default=str, separators=(",", ":"))
        data_json = json.dumps(data, default=str, separators=(",", ":"))

        # Log in the standardized format
        logger.info(
            f"INFO | {operation_type} | {unique_id} | {headers_json} | {data_json}"
        )

    except Exception as e:
        # Fallback logging if there's an error
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(f"ERROR | LOGGING_FAILURE | {unique_id} | {{}} | {error_log}")


def log_response_info(
    unique_id, response_data, status_code=200, operation_type="RESPONSE"
):
    """
    Logs response information in a standardized JSON format.

    Args:
        unique_id (str): Unique identifier correlating with the request
        response_data: Response data to log
        status_code (int): HTTP status code
        operation_type (str): Type of operation (RESPONSE, SUCCESS, ERROR, etc.)
    """
    try:
        log_data = {
            "unique_id": unique_id,
            "timestamp": timezone.now().isoformat(),
            "status_code": status_code,
            "response_data": response_data,
        }

        response_json = json.dumps(log_data, default=str, separators=(",", ":"))
        logger.info(f"INFO | {operation_type} | {unique_id} | {{}} | {response_json}")

    except Exception as e:
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(f"ERROR | LOGGING_FAILURE | {unique_id} | {{}} | {error_log}")


def log_operation_info(unique_id, operation_type, message, metadata=None, level="INFO"):
    """
    Logs operation information in a standardized format.

    Args:
        unique_id (str): Unique identifier for correlation
        operation_type (str): Type of operation (USER_CREATION, OAUTH_APP_CREATION, etc.)
        message (str): Descriptive message
        metadata (dict): Additional metadata to include
        level (str): Log level (INFO, WARNING, ERROR)
    """
    try:
        log_data = {
            "unique_id": unique_id,
            "timestamp": timezone.now().isoformat(),
            "operation": operation_type,
            "message": message,
            "metadata": metadata or {},
        }

        log_json = json.dumps(log_data, default=str, separators=(",", ":"))

        if level == "ERROR":
            logger.error(f"ERROR | {operation_type} | {unique_id} | {{}} | {log_json}")
        elif level == "WARNING":
            logger.warning(
                f"WARNING | {operation_type} | {unique_id} | {{}} | {log_json}"
            )
        else:
            logger.info(f"INFO | {operation_type} | {unique_id} | {{}} | {log_json}")

    except Exception as e:
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(f"ERROR | LOGGING_FAILURE | {unique_id} | {{}} | {error_log}")


def log_security_event_standardized(
    unique_id,
    event_type,
    description,
    user=None,
    request=None,
    metadata=None,
    level="WARNING",
):
    """
    Log security events in a standardized format.

    Args:
        unique_id (str): Unique identifier for correlation
        event_type (str): Type of security event (LOGIN_FAILURE, SUSPICIOUS_ACTIVITY, etc.)
        description (str): Description of the security event
        user: User object (optional)
        request: Django request object (optional)
        metadata (dict): Additional metadata
        level (str): Log level (INFO, WARNING, ERROR)
    """
    try:
        security_metadata = metadata or {}

        # Add user information if available
        if user:
            security_metadata.update(
                {
                    "user_id": getattr(user, "id", None),
                    "user_email": getattr(user, "email", None),
                    "user_role": getattr(user, "role", None),
                }
            )

        # Add request information if available
        if request:
            security_metadata.update(
                {
                    "client_ip": get_client_ip(request),
                    "user_agent": (
                        request.META.get("HTTP_USER_AGENT", "")
                        if hasattr(request, "META")
                        else ""
                    ),
                    "request_method": getattr(request, "method", "UNKNOWN"),
                    "request_path": getattr(request, "path", ""),
                }
            )

        log_operation_info(
            unique_id=unique_id,
            operation_type=f"SECURITY_{event_type}",
            message=description,
            metadata=security_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(
            f"ERROR | SECURITY_LOGGING_FAILURE | {unique_id} | {{}} | {error_log}"
        )


def log_database_operation(
    unique_id,
    operation_type,
    table_name,
    operation_result=None,
    metadata=None,
    level="INFO",
):
    """
    Log database operations in a standardized format.

    Args:
        unique_id (str): Unique identifier for correlation
        operation_type (str): Type of database operation (CREATE, UPDATE, DELETE, SELECT)
        table_name (str): Name of the database table/model
        operation_result (str): Result of the operation (SUCCESS, FAILURE, etc.)
        metadata (dict): Additional metadata (record_id, affected_rows, etc.)
        level (str): Log level
    """
    try:
        db_metadata = metadata or {}
        db_metadata.update(
            {
                "table_name": table_name,
                "operation_result": operation_result or "UNKNOWN",
            }
        )

        log_operation_info(
            unique_id=unique_id,
            operation_type=f"DB_{operation_type}",
            message=f"Database {operation_type.lower()} operation on {table_name}",
            metadata=db_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(f"ERROR | DB_LOGGING_FAILURE | {unique_id} | {{}} | {error_log}")


def log_api_call(
    unique_id,
    api_name,
    endpoint,
    method="GET",
    status_code=None,
    response_time=None,
    metadata=None,
    level="INFO",
):
    """
    Log external API calls in a standardized format.

    Args:
        unique_id (str): Unique identifier for correlation
        api_name (str): Name of the external API
        endpoint (str): API endpoint called
        method (str): HTTP method used
        status_code (int): HTTP status code received
        response_time (float): Response time in seconds
        metadata (dict): Additional metadata
        level (str): Log level
    """
    try:
        api_metadata = metadata or {}
        api_metadata.update(
            {
                "api_name": api_name,
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time_seconds": response_time,
            }
        )

        message = f"API call to {api_name} - {method} {endpoint}"
        if status_code:
            message += f" - Status: {status_code}"
        if response_time:
            message += f" - Time: {response_time:.3f}s"

        log_operation_info(
            unique_id=unique_id,
            operation_type="API_CALL",
            message=message,
            metadata=api_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(f"ERROR | API_LOGGING_FAILURE | {unique_id} | {{}} | {error_log}")


def log_business_event(
    unique_id,
    event_type,
    description,
    entity_type=None,
    entity_id=None,
    metadata=None,
    level="INFO",
):
    """
    Log business events in a standardized format.

    Args:
        unique_id (str): Unique identifier for correlation
        event_type (str): Type of business event (ORDER_CREATED, PAYMENT_PROCESSED, etc.)
        description (str): Description of the business event
        entity_type (str): Type of business entity (ORDER, PAYMENT, USER, etc.)
        entity_id (str): ID of the business entity
        metadata (dict): Additional metadata
        level (str): Log level
    """
    try:
        business_metadata = metadata or {}
        if entity_type:
            business_metadata["entity_type"] = entity_type
        if entity_id:
            business_metadata["entity_id"] = entity_id

        log_operation_info(
            unique_id=unique_id,
            operation_type=f"BUSINESS_{event_type}",
            message=description,
            metadata=business_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(
            f"ERROR | BUSINESS_LOGGING_FAILURE | {unique_id} | {{}} | {error_log}"
        )


def log_performance_metric(
    unique_id,
    metric_name,
    metric_value,
    unit="ms",
    operation_type=None,
    metadata=None,
    level="INFO",
):
    """
    Log performance metrics in a standardized format.

    Args:
        unique_id (str): Unique identifier for correlation
        metric_name (str): Name of the performance metric
        metric_value (float): Value of the metric
        unit (str): Unit of measurement (ms, seconds, bytes, etc.)
        operation_type (str): Type of operation being measured
        metadata (dict): Additional metadata
        level (str): Log level
    """
    try:
        perf_metadata = metadata or {}
        perf_metadata.update(
            {
                "metric_name": metric_name,
                "metric_value": metric_value,
                "unit": unit,
            }
        )

        if operation_type:
            perf_metadata["operation_type"] = operation_type

        message = f"Performance metric: {metric_name} = {metric_value} {unit}"
        if operation_type:
            message += f" for {operation_type}"

        log_operation_info(
            unique_id=unique_id,
            operation_type="PERFORMANCE_METRIC",
            message=message,
            metadata=perf_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e), "unique_id": unique_id}, default=str)
        logger.error(
            f"ERROR | PERFORMANCE_LOGGING_FAILURE | {unique_id} | {{}} | {error_log}"
        )


def create_logging_context(
    unique_id: str, request=None, user=None, operation_name=None
):
    """
    Create a logging context with unique ID and common metadata.

    Args:
        unique_id (str): Unique identifier for correlation. This is mandatory.
        request: Django request object (optional)
        user: User object (optional)
        operation_name (str): Name of the operation (optional)

    Returns:
        dict: Logging context with unique_id and metadata

    Raises:
        ValueError: If unique_id is not provided.
    """
    try:

        context = {"unique_id": unique_id, "metadata": {}}

        if operation_name:
            context["operation_name"] = operation_name

        if request:
            context["metadata"].update(
                {
                    "client_ip": get_client_ip(request),
                    "user_agent": (
                        request.META.get("HTTP_USER_AGENT", "")
                        if hasattr(request, "META")
                        else ""
                    ),
                    "request_method": getattr(request, "method", "UNKNOWN"),
                    "request_path": getattr(request, "path", ""),
                }
            )

        if user:
            context["metadata"].update(
                {
                    "user_id": getattr(user, "id", None),
                    "user_email": getattr(user, "email", None),
                    "user_role": getattr(user, "role", None),
                }
            )

        return context

    except Exception as e:
        # Fallback context if creation fails
        return {
            "unique_id": f"FALLBACK_{uuid.uuid4().hex[:8]}",
            "metadata": {"error": str(e)},
        }
