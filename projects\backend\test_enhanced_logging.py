#!/usr/bin/env python
"""
Test script to demonstrate the enhanced logging functionality.
Run this script to see the enhanced logging in action.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
django.setup()

# Now import the logging utilities
from agritram.logger_utils import (
    enhanced_log,
    generate_api_call_uuid,
    log_api_call,
    log_error_with_traceback,
)


def test_basic_logging():
    """Test basic enhanced logging functionality."""
    print("\n=== Testing Basic Logging ===")
    
    # Test info logging
    unique_id = enhanced_log(
        message="Testing enhanced logging system",
        level="INFO",
        operation_type="LOGGING_TEST",
        metadata={"test_type": "basic", "version": "1.0"}
    )
    print(f"✓ Info log created with UUID: {unique_id}")
    
    # Test warning logging with same UUID
    enhanced_log(
        message="This is a warning message",
        level="WARNING",
        operation_type="LOGGING_TEST_WARNING",
        metadata={"warning_type": "test_warning"},
        unique_id=unique_id
    )
    print(f"✓ Warning log created with same UUID: {unique_id}")


def test_error_logging():
    """Test error logging with exception handling."""
    print("\n=== Testing Error Logging ===")
    
    try:
        # Intentionally cause an error
        result = 10 / 0
    except Exception as e:
        unique_id = enhanced_log(
            message="Intentional division by zero for testing",
            level="ERROR",
            operation_type="LOGGING_TEST_ERROR",
            metadata={
                "test_type": "error_handling",
                "numerator": 10,
                "denominator": 0
            },
            exception=e
        )
        print(f"✓ Error log created with UUID: {unique_id}")
        print(f"✓ Exception details captured: {e.__class__.__name__}")


def test_api_call_logging():
    """Test API call logging."""
    print("\n=== Testing API Call Logging ===")
    
    # Test successful API call
    unique_id = log_api_call(
        api_name="TestAPI",
        endpoint="/api/v1/test",
        method="GET",
        status_code=200,
        response_time=0.123,
        metadata={
            "test_data": "success",
            "response_size": 1024
        }
    )
    print(f"✓ Successful API call logged with UUID: {unique_id}")
    
    # Test failed API call
    unique_id = log_api_call(
        api_name="TestAPI",
        endpoint="/api/v1/test",
        method="POST",
        status_code=500,
        response_time=2.456,
        metadata={
            "test_data": "failure",
            "error_code": "INTERNAL_ERROR"
        },
        level="ERROR"
    )
    print(f"✓ Failed API call logged with UUID: {unique_id}")


def test_business_operation_logging():
    """Test business operation logging with correlation."""
    print("\n=== Testing Business Operation Logging ===")
    
    # Generate a single UUID for the entire business operation
    operation_id = generate_api_call_uuid()
    print(f"✓ Generated operation UUID: {operation_id}")
    
    # Log start of operation
    enhanced_log(
        message="Starting crop analysis operation",
        level="INFO",
        operation_type="CROP_ANALYSIS_START",
        metadata={
            "crop_type": "wheat",
            "field_id": "test_field_001",
            "analysis_type": "yield_prediction"
        },
        unique_id=operation_id
    )
    print("✓ Operation start logged")
    
    # Log progress
    enhanced_log(
        message="Soil data analysis completed",
        level="INFO",
        operation_type="CROP_ANALYSIS_PROGRESS",
        metadata={
            "soil_ph": 6.5,
            "moisture_level": 45.2,
            "nutrient_score": 8.7
        },
        unique_id=operation_id
    )
    print("✓ Operation progress logged")
    
    # Log completion
    enhanced_log(
        message="Crop analysis operation completed successfully",
        level="INFO",
        operation_type="CROP_ANALYSIS_COMPLETE",
        metadata={
            "yield_prediction": 85.6,
            "confidence_score": 0.94,
            "processing_time_ms": 1250.5
        },
        unique_id=operation_id
    )
    print("✓ Operation completion logged")


def test_file_line_detection():
    """Test that file names and line numbers are correctly detected."""
    print("\n=== Testing File/Line Detection ===")
    
    def inner_function():
        """Inner function to test call stack detection."""
        return enhanced_log(
            message="Log from inner function",
            level="INFO",
            operation_type="FILE_LINE_TEST",
            metadata={"function": "inner_function", "depth": 2}
        )
    
    def outer_function():
        """Outer function to test call stack detection."""
        return inner_function()
    
    # Test direct call
    unique_id1 = enhanced_log(
        message="Direct function call",
        level="INFO",
        operation_type="FILE_LINE_TEST",
        metadata={"function": "test_file_line_detection", "depth": 1}
    )
    print(f"✓ Direct call logged with UUID: {unique_id1}")
    
    # Test nested call
    unique_id2 = outer_function()
    print(f"✓ Nested call logged with UUID: {unique_id2}")


def main():
    """Run all logging tests."""
    print("🚀 Starting Enhanced Logging System Tests")
    print("=" * 50)
    
    try:
        test_basic_logging()
        test_error_logging()
        test_api_call_logging()
        test_business_operation_logging()
        test_file_line_detection()
        
        print("\n" + "=" * 50)
        print("✅ All logging tests completed successfully!")
        print("\n📁 Check the following log files for output:")
        print("   - logs/api.log")
        print("   - logs/errors.log")
        print("   - Console output above")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
