from django.db.models.signals import post_save
from django.dispatch import receiver
from crops.models import CropTransfer, Crops
from .models import InventoryQuantity, InventoryCropStatus
from user.models import RoleChoices
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)

from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from oauth2_provider.contrib.rest_framework import OAuth2Authentication
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .serializers import (
    CropsWithStatusSerializer,
    InventoryQuantitySerializer,
    TraderInventoryQuantitySerializer,
)
from rest_framework import status
from django.db.models import F
from rest_framework import permissions
from oauth2_auth.permissions import AdminPermission
from user.permissions import UserStatusMixin
from agritram.message_utils import (
    handle_exception_with_logging,
    StandardSuccessResponse,
)
from agritram.exceptions import (
    raise_validation_error,
    raise_authorization_error,
    raise_not_found_error,
)
from agritram.logger_utils import (
    generate_unique_request_id,
    log_request_info,
    log_response_info,
    log_operation_info,
    create_logging_context,
)


class InventoryViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for viewing inventory.
    Business Rule: Only traders and manufacturers can see trader inventory.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            # Generate unique ID for logging
            unique_id = generate_unique_request_id()
            log_operation_info(
                unique_id,
                "INVENTORY_PERMISSION_DENIED",
                f"Inventory view permission denied for {request.user.email}",
                metadata={
                    "user_email": request.user.email,
                    "user_role": getattr(request.user, "role", "unknown"),
                    "denial_reason": reason,
                    "permission_type": "inventory_view",
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # Only traders, manufacturers, and admins can view inventory
        return user_role in ["trader", "manufacturer", "admin"]


class InventoryOwnerPermission(permissions.BasePermission):
    """
    Permission for updating inventory.
    Business Rule: Only the trader who owns the inventory can update it.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        user_role = request.user.role

        # Only traders and admins can update inventory
        return user_role in ["trader", "admin"]


class TraderInventoryViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for viewing specific trader's inventory.
    Business Rule: Only that trader and manufacturers who want to buy can view.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            # Generate unique ID for logging
            unique_id = generate_unique_request_id()
            log_operation_info(
                unique_id,
                "TRADER_INVENTORY_PERMISSION_DENIED",
                f"Trader inventory view permission denied for {request.user.email}",
                metadata={
                    "user_email": request.user.email,
                    "user_role": getattr(request.user, "role", "unknown"),
                    "denial_reason": reason,
                    "permission_type": "trader_inventory_view",
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # Traders (owners), manufacturers (buyers), and admins can view
        return user_role in ["trader", "manufacturer", "admin"]

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can view everything
        if user_role == "admin":
            return True

        # Trader can view their own inventory
        if (
            user_role == "trader"
            and hasattr(obj, "trader")
            and obj.trader == request.user
        ):
            return True

        # Manufacturers can view any trader's inventory (for buying purposes)
        if user_role == "manufacturer":
            return True

        return False


@receiver(post_save, sender=CropTransfer)
def update_inventory(sender, instance, **kwargs):
    trader = instance.to_user
    if trader.role != RoleChoices.TRADER:
        return
    crop = instance.crop
    quantity = crop.quantity
    unit = crop.unit

    if unit != "tons":
        quantity = quantity / 1000

    inventory, create = InventoryQuantity.objects.get_or_create(
        trader=trader,
        defaults={
            "total_quantity_to_date": quantity,
            "total_quantity_in_storage": quantity,
            "total_quantity_batches": 1,
            "storage_batches": 1,
            "ready_to_sell_quantity": 0,
            "sold_quantity": 0,
            "ready_to_sell_batches": 0,
            "sold_batches": 0,
        },
    )
    inventorycropstatus, created = InventoryCropStatus.objects.get_or_create(
        trader=trader,
        crop=crop,
        defaults={
            "status": "storage",
        },
    )
    if create:
        inventory.save()
    else:
        inventory.total_quantity_to_date += quantity
        inventory.total_quantity_in_storage += quantity
        inventory.total_quantity_batches += 1
        inventory.storage_batches += 1
        inventory.save()
    if created:
        inventorycropstatus.save()
    else:
        pass


@api_view(["GET"])
@permission_classes(
    [InventoryViewPermission]
)  # Only traders and manufacturers can view inventory
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_inventory(request):
    """
    Retrieve inventory data for the authenticated trader.
    Business Rule: Only traders and manufacturers can see trader inventory.
    """
    # Generate unique request ID for logging correlation
    unique_id = generate_unique_request_id()

    try:
        # Log incoming request
        log_request_info(unique_id, request, "GET_INVENTORY_REQUEST")

        # Create logging context
        logging_context = create_logging_context(
            unique_id, request, request.user, "get_inventory"
        )

        if request.user.role == "trader":
            # Traders can only see their own inventory
            log_operation_info(
                unique_id,
                "INVENTORY_ACCESS",
                "Trader accessing own inventory",
                metadata={
                    **logging_context["metadata"],
                    "access_type": "own_inventory",
                },
            )
            inventory = get_object_or_404(InventoryQuantity, trader=request.user)
        elif request.user.role == "manufacturer":
            # Manufacturers should specify which trader's inventory they want to see
            log_operation_info(
                unique_id,
                "INVENTORY_ACCESS_ERROR",
                "Manufacturer attempted to access general inventory endpoint",
                metadata={
                    **logging_context["metadata"],
                    "error_type": "wrong_endpoint",
                },
                level="WARNING",
            )
            raise_validation_error(
                message="Manufacturers should use /trader/{trader_id}/inventory/ endpoint",
                details="This endpoint is for individual trader inventory access",
            )
        elif request.user.role == "admin":
            # Admins can see all inventories, but this endpoint is for single trader
            log_operation_info(
                unique_id,
                "INVENTORY_ACCESS_ERROR",
                "Admin attempted to access single trader inventory endpoint",
                metadata={
                    **logging_context["metadata"],
                    "error_type": "wrong_endpoint",
                },
                level="WARNING",
            )
            raise_validation_error(
                message="Admins should use /entire-inventory/ endpoint",
                details="This endpoint is for individual trader inventory access",
            )
        else:
            log_operation_info(
                unique_id,
                "INVENTORY_ACCESS_DENIED",
                "Unauthorized role attempted to access inventory",
                metadata={
                    **logging_context["metadata"],
                    "error_type": "unauthorized_role",
                },
                level="WARNING",
            )
            raise_authorization_error(
                message="Access denied",
                details="Only traders, manufacturers, and admins can access inventory",
            )

        serializer = InventoryQuantitySerializer(inventory)

        # Log successful response
        log_response_info(
            unique_id,
            {
                "message": "Inventory retrieved successfully",
                "inventory_data": serializer.data,
            },
            200,
            "GET_INVENTORY_SUCCESS",
        )

        return StandardSuccessResponse.data_retrieved(
            message="Inventory retrieved successfully",
            details="Current inventory data for authenticated trader",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "get_inventory", request, request.user, unique_id
        )


@api_view(["GET"])
@permission_classes([AdminPermission])  # Only admins can view entire inventory
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_entire_inventory(request):
    """
    Retrieve all inventory data.
    Business Rule: Only admins can view entire inventory.
    """
    # Generate unique request ID for logging correlation
    unique_id = generate_unique_request_id()

    try:
        # Log incoming request
        log_request_info(unique_id, request, "GET_ENTIRE_INVENTORY_REQUEST")

        # Create logging context
        logging_context = create_logging_context(
            unique_id, request, request.user, "get_entire_inventory"
        )

        # Log admin access
        log_operation_info(
            unique_id,
            "ENTIRE_INVENTORY_ACCESS",
            "Admin accessing entire inventory",
            metadata={
                **logging_context["metadata"],
                "access_type": "entire_inventory",
            },
        )

        inventory = InventoryQuantity.objects.all()
        serializer = TraderInventoryQuantitySerializer(inventory, many=True)

        # Log successful response
        log_response_info(
            unique_id,
            {
                "message": "Entire inventory retrieved successfully",
                "traders_count": len(serializer.data),
            },
            200,
            "GET_ENTIRE_INVENTORY_SUCCESS"
        )

        return StandardSuccessResponse.data_retrieved(
            message="Entire inventory retrieved successfully",
            details=f"Retrieved inventory data for {len(serializer.data)} traders",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "get_entire_inventory", request, request.user, unique_id
        )


@api_view(["GET"])
@permission_classes(
    [InventoryViewPermission]
)  # Only traders and manufacturers can view crop status
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_inventory_crop_status(request):
    """
    Get inventory crop status for the authenticated trader.
    Business Rule: Only traders can see their own crop status.
    """
    # Generate unique request ID for logging correlation
    unique_id = generate_unique_request_id()

    try:
        # Log incoming request
        log_request_info(unique_id, request, "GET_INVENTORY_CROP_STATUS_REQUEST")

        # Create logging context
        logging_context = create_logging_context(
            unique_id, request, request.user, "get_inventory_crop_status"
        )

        if request.user.role != "trader":
            log_operation_info(
                unique_id,
                "CROP_STATUS_ACCESS_DENIED",
                "Non-trader attempted to access crop status",
                metadata={
                    **logging_context["metadata"],
                    "error_type": "unauthorized_role",
                },
                level="WARNING"
            )
            raise_authorization_error(
                message="Only traders can access their crop status",
                details="This endpoint is restricted to trader role users",
            )

        log_operation_info(
            unique_id,
            "CROP_STATUS_ACCESS",
            "Trader accessing own crop status",
            metadata={
                **logging_context["metadata"],
                "access_type": "own_crop_status",
            },
        )

        inventory_status_qs = InventoryCropStatus.objects.filter(
            trader=request.user
        ).select_related("crop")
        serializer = CropsWithStatusSerializer(inventory_status_qs, many=True)

        # Log successful response
        log_response_info(
            unique_id,
            {
                "message": "Inventory crop status retrieved successfully",
                "crops_count": len(serializer.data),
            },
            200,
            "GET_INVENTORY_CROP_STATUS_SUCCESS"
        )

        return StandardSuccessResponse.data_retrieved(
            message="Inventory crop status retrieved successfully",
            details=f"Retrieved status for {len(serializer.data)} crops in your inventory",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "get_inventory_crop_status", request, request.user, unique_id
        )


@api_view(["GET"])
@permission_classes(
    [TraderInventoryViewPermission]
)  # Trader owners and manufacturers can view
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_trader_inventory_crop_status(request, trader_id):
    """
    Get crop status for a specific trader's inventory.
    Business Rule: Only that trader and manufacturers who want to buy can view.
    """
    # Generate unique request ID for logging correlation
    unique_id = generate_unique_request_id()

    try:
        # Log incoming request
        log_request_info(unique_id, request, "GET_TRADER_INVENTORY_CROP_STATUS_REQUEST")

        # Create logging context
        logging_context = create_logging_context(
            unique_id, request, request.user, "get_trader_inventory_crop_status"
        )

        from user.models import User

        try:
            trader = User.objects.get(id=trader_id, role="trader")
        except User.DoesNotExist:
            log_operation_info(
                unique_id,
                "TRADER_NOT_FOUND",
                f"Trader not found with ID {trader_id}",
                metadata={
                    **logging_context["metadata"],
                    "trader_id": trader_id,
                    "error_type": "trader_not_found",
                },
                level="WARNING"
            )
            raise_not_found_error(
                message="Trader not found",
                details=f"No trader found with ID {trader_id}",
                resource_type="trader",
                resource_id=str(trader_id),
            )

        # Check permissions
        if request.user.role == "trader" and request.user != trader:
            log_operation_info(
                unique_id,
                "TRADER_INVENTORY_ACCESS_DENIED",
                "Trader attempted to access another trader's inventory",
                metadata={
                    **logging_context["metadata"],
                    "target_trader_id": trader_id,
                    "error_type": "cross_trader_access",
                },
                level="WARNING"
            )
            raise_authorization_error(
                message="Traders can only view their own inventory",
                details="You can only access your own inventory data",
            )
        elif request.user.role not in ["trader", "manufacturer", "admin"]:
            log_operation_info(
                unique_id,
                "TRADER_INVENTORY_ACCESS_DENIED",
                "Unauthorized role attempted to access trader inventory",
                metadata={
                    **logging_context["metadata"],
                    "target_trader_id": trader_id,
                    "error_type": "unauthorized_role",
                },
                level="WARNING"
            )
            raise_authorization_error(
                message="Access denied",
                details="Only traders, manufacturers, and admins can access inventory data",
            )

        log_operation_info(
            unique_id,
            "TRADER_INVENTORY_ACCESS",
            f"Accessing trader {trader_id} inventory crop status",
            metadata={
                **logging_context["metadata"],
                "target_trader_id": trader_id,
                "access_type": "trader_inventory_crop_status",
            },
        )

        inventory_status_qs = InventoryCropStatus.objects.filter(
            trader=trader_id, status="ready"
        ).select_related("crop")
        serializer = CropsWithStatusSerializer(inventory_status_qs, many=True)

        # Log successful response
        log_response_info(
            unique_id,
            {
                "message": "Trader inventory crop status retrieved successfully",
                "trader_id": trader_id,
                "ready_crops_count": len(serializer.data),
            },
            200,
            "GET_TRADER_INVENTORY_CROP_STATUS_SUCCESS"
        )

        return StandardSuccessResponse.data_retrieved(
            message="Trader inventory crop status retrieved successfully",
            details=f"Retrieved {len(serializer.data)} ready crops from trader {trader_id}",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "get_trader_inventory_crop_status", request, request.user, unique_id
        )


@api_view(["PUT"])
@permission_classes([InventoryOwnerPermission])  # Only trader owners can update
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def update_crop_status(request, crop_id):
    """
    Update crop status in inventory.
    Business Rule: Only the trader who owns the inventory can update it.
    """
    # Generate unique request ID for logging correlation
    unique_id = generate_unique_request_id()

    try:
        # Log incoming request
        log_request_info(unique_id, request, "UPDATE_CROP_STATUS_REQUEST")

        # Create logging context
        logging_context = create_logging_context(
            unique_id, request, request.user, "update_crop_status"
        )

        # Ensure only traders can update their own inventory
        if request.user.role != "trader":
            log_operation_info(
                unique_id,
                "CROP_STATUS_UPDATE_DENIED",
                "Non-trader attempted to update crop status",
                metadata={
                    **logging_context["metadata"],
                    "crop_id": crop_id,
                    "error_type": "unauthorized_role",
                },
                level="WARNING"
            )
            raise_authorization_error(
                message="Only traders can update crop status",
                details="This operation is restricted to trader role users",
            )

        crop = get_object_or_404(Crops, crop_id=crop_id)
        crop_status = request.data.get("status")

        log_operation_info(
            unique_id,
            "CROP_STATUS_UPDATE_START",
            f"Starting crop status update for crop {crop_id}",
            metadata={
                **logging_context["metadata"],
                "crop_id": crop_id,
                "new_status": crop_status,
                "operation_type": "crop_status_update",
            },
        )

        # Verify the trader owns this crop in their inventory
        try:
            InventoryCropStatus.objects.get(crop=crop, trader=request.user)
        except InventoryCropStatus.DoesNotExist:
            log_operation_info(
                unique_id,
                "CROP_NOT_IN_INVENTORY",
                f"Crop {crop_id} not found in trader's inventory",
                metadata={
                    **logging_context["metadata"],
                    "crop_id": crop_id,
                    "error_type": "crop_not_owned",
                },
                level="WARNING"
            )
            raise_not_found_error(
                message="Crop not found in your inventory",
                details=f"Crop with ID {crop_id} is not in your inventory",
                resource_type="inventory_crop",
                resource_id=crop_id,
            )

        # Update crop status
        InventoryCropStatus.objects.filter(crop=crop, trader=request.user).update(
            status=crop_status
        )

        log_operation_info(
            unique_id,
            "CROP_STATUS_UPDATED",
            f"Crop status updated to {crop_status}",
            metadata={
                **logging_context["metadata"],
                "crop_id": crop_id,
                "new_status": crop_status,
                "operation_type": "status_update",
            },
        )

        # Update inventory quantities based on status change
        if crop_status == "storage":
            InventoryQuantity.objects.filter(trader=request.user).update(
                total_quantity_in_storage=F("total_quantity_in_storage") + crop.quantity,
                ready_to_sell_quantity=F("ready_to_sell_quantity") - crop.quantity,
                storage_batches=F("storage_batches") + 1,
                ready_to_sell_batches=F("ready_to_sell_batches") - 1,
            )
            log_operation_info(
                unique_id,
                "INVENTORY_QUANTITIES_UPDATED",
                f"Inventory quantities updated for storage status",
                metadata={
                    **logging_context["metadata"],
                    "crop_id": crop_id,
                    "status_change": "to_storage",
                    "crop_quantity": crop.quantity,
                },
            )
        elif crop_status == "ready":
            InventoryQuantity.objects.filter(trader=request.user).update(
                total_quantity_in_storage=F("total_quantity_in_storage") - crop.quantity,
                ready_to_sell_quantity=F("ready_to_sell_quantity") + crop.quantity,
                storage_batches=F("storage_batches") - 1,
                ready_to_sell_batches=F("ready_to_sell_batches") + 1,
            )
            log_operation_info(
                unique_id,
                "INVENTORY_QUANTITIES_UPDATED",
                f"Inventory quantities updated for ready status",
                metadata={
                    **logging_context["metadata"],
                    "crop_id": crop_id,
                    "status_change": "to_ready",
                    "crop_quantity": crop.quantity,
                },
            )

        # Log successful response
        log_response_info(
            unique_id,
            {
                "message": "Crop status updated successfully",
                "crop_id": crop_id,
                "new_status": crop_status,
            },
            200,
            "UPDATE_CROP_STATUS_SUCCESS"
        )

        return StandardSuccessResponse.record_updated(
            message="Crop status updated successfully",
            details=f"Crop {crop_id} status changed to '{crop_status}' and inventory quantities updated",
            record_data={"crop_id": crop_id, "new_status": crop_status},
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "update_crop_status", request, request.user, unique_id
        )
