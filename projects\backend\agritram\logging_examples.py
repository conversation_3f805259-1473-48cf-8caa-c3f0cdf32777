"""
Examples of how to use the enhanced logging system with unique IDs,
file names, and line numbers.
"""

from .logger_utils import (
    enhanced_log,
    generate_api_call_uuid,
    log_api_call,
    log_error_with_traceback,
    log_operation_info,
)


def example_basic_logging():
    """Example of basic enhanced logging."""
    
    # Simple info log with auto-generated UUID
    unique_id = enhanced_log(
        message="User registration process started",
        level="INFO",
        operation_type="USER_REGISTRATION",
        metadata={"email": "<EMAIL>"}
    )
    print(f"Generated unique ID: {unique_id}")
    
    # Warning log with specific UUID
    enhanced_log(
        message="Password strength is weak",
        level="WARNING",
        operation_type="PASSWORD_VALIDATION",
        metadata={"strength_score": 2},
        unique_id=unique_id  # Use same ID for correlation
    )


def example_error_logging():
    """Example of error logging with exception details."""
    
    try:
        # Simulate an error
        result = 10 / 0
    except Exception as e:
        # Log error with full traceback and file/line info
        unique_id = enhanced_log(
            message="Division by zero error in calculation",
            level="ERROR",
            operation_type="CALCULATION_ERROR",
            metadata={"operation": "division", "numerator": 10, "denominator": 0},
            exception=e
        )
        print(f"Error logged with ID: {unique_id}")


def example_api_call_logging():
    """Example of API call logging."""
    
    # Log an external API call
    unique_id = log_api_call(
        api_name="PaymentGateway",
        endpoint="/api/v1/payments",
        method="POST",
        status_code=200,
        response_time=0.245,
        metadata={
            "payment_id": "pay_123456",
            "amount": 100.00,
            "currency": "USD"
        }
    )
    print(f"API call logged with ID: {unique_id}")


def example_business_operation_logging():
    """Example of business operation logging."""
    
    # Generate a unique ID for the entire operation
    operation_id = generate_api_call_uuid()
    
    # Log start of operation
    enhanced_log(
        message="Starting crop data analysis",
        level="INFO",
        operation_type="CROP_ANALYSIS_START",
        metadata={"crop_type": "wheat", "field_id": "field_001"},
        unique_id=operation_id
    )
    
    # Log progress
    enhanced_log(
        message="Soil data retrieved successfully",
        level="INFO",
        operation_type="CROP_ANALYSIS_PROGRESS",
        metadata={"soil_ph": 6.5, "moisture": 45},
        unique_id=operation_id
    )
    
    # Log completion
    enhanced_log(
        message="Crop analysis completed",
        level="INFO",
        operation_type="CROP_ANALYSIS_COMPLETE",
        metadata={"yield_prediction": 85.2, "confidence": 0.92},
        unique_id=operation_id
    )


def example_database_operation_logging():
    """Example of database operation logging."""
    
    unique_id = generate_api_call_uuid()
    
    try:
        # Simulate database operation
        enhanced_log(
            message="Creating new user record",
            level="INFO",
            operation_type="DB_CREATE_USER",
            metadata={
                "table": "users",
                "email": "<EMAIL>",
                "role": "farmer"
            },
            unique_id=unique_id
        )
        
        # Simulate success
        enhanced_log(
            message="User record created successfully",
            level="INFO",
            operation_type="DB_CREATE_USER_SUCCESS",
            metadata={
                "user_id": 12345,
                "created_at": "2025-01-28T10:30:00Z"
            },
            unique_id=unique_id
        )
        
    except Exception as e:
        # Log database error
        enhanced_log(
            message="Failed to create user record",
            level="ERROR",
            operation_type="DB_CREATE_USER_ERROR",
            metadata={
                "table": "users",
                "error_code": "DUPLICATE_EMAIL"
            },
            unique_id=unique_id,
            exception=e
        )


def example_security_event_logging():
    """Example of security event logging."""
    
    # Log suspicious activity
    unique_id = enhanced_log(
        message="Multiple failed login attempts detected",
        level="WARNING",
        operation_type="SECURITY_FAILED_LOGIN",
        metadata={
            "email": "<EMAIL>",
            "ip_address": "*************",
            "attempts": 5,
            "time_window": "5 minutes"
        }
    )
    
    # Log security action taken
    enhanced_log(
        message="Account temporarily locked due to suspicious activity",
        level="WARNING",
        operation_type="SECURITY_ACCOUNT_LOCKED",
        metadata={
            "lock_duration": "30 minutes",
            "reason": "multiple_failed_attempts"
        },
        unique_id=unique_id
    )


def example_performance_logging():
    """Example of performance monitoring logging."""
    
    import time
    
    unique_id = generate_api_call_uuid()
    start_time = time.time()
    
    # Simulate some work
    time.sleep(0.1)
    
    end_time = time.time()
    duration = (end_time - start_time) * 1000  # Convert to milliseconds
    
    # Log performance metric
    enhanced_log(
        message="Image processing completed",
        level="INFO",
        operation_type="PERFORMANCE_IMAGE_PROCESSING",
        metadata={
            "image_size": "1920x1080",
            "processing_time_ms": round(duration, 2),
            "algorithm": "crop_detection_v2",
            "objects_detected": 15
        },
        unique_id=unique_id
    )


if __name__ == "__main__":
    """Run examples to demonstrate logging functionality."""
    
    print("=== Enhanced Logging Examples ===\n")
    
    print("1. Basic Logging:")
    example_basic_logging()
    print()
    
    print("2. Error Logging:")
    example_error_logging()
    print()
    
    print("3. API Call Logging:")
    example_api_call_logging()
    print()
    
    print("4. Business Operation Logging:")
    example_business_operation_logging()
    print()
    
    print("5. Database Operation Logging:")
    example_database_operation_logging()
    print()
    
    print("6. Security Event Logging:")
    example_security_event_logging()
    print()
    
    print("7. Performance Logging:")
    example_performance_logging()
    print()
    
    print("=== Examples Complete ===")
