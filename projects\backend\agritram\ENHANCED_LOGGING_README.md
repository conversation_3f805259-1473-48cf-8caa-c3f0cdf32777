# Enhanced Logging System

This document describes the enhanced logging system that provides comprehensive logging with unique IDs, file names, line numbers, and structured data for better debugging and monitoring.

## Features

- **Unique UUIDs**: Every API call and operation gets a unique identifier for correlation
- **File Names & Line Numbers**: Automatic capture of source file and line number for errors
- **Structured Logging**: JSON-formatted logs with consistent structure
- **Caller Information**: Automatic detection of calling function and location
- **Exception Tracking**: Full traceback information for errors
- **Performance Monitoring**: Response time tracking for API calls
- **Security Event Logging**: Specialized logging for security-related events

## Log Format

The enhanced logging system uses the following format:

```
{LEVEL} | {OPERATION_TYPE} | {UNIQUE_ID} | {FILENAME}:{LINE_NUMBER} | {JSON_DATA}
```

### Examples:

**Info Log:**
```
INFO | USER_REGISTRATION | API_a1b2c3d4-e5f6-7890-abcd-ef1234567890 | {} | {"unique_id":"API_a1b2c3d4-e5f6-7890-abcd-ef1234567890","timestamp":"2025-01-28T10:30:00.123Z","operation":"USER_REGISTRATION","message":"User registration process started","metadata":{"email":"<EMAIL>"},"caller":{"filename":"views.py","line_number":45,"function_name":"register_user"}}
```

**Error Log:**
```
ERROR | CALCULATION_ERROR | API_b2c3d4e5-f6g7-8901-bcde-f23456789012 | calculations.py:123 | {"unique_id":"API_b2c3d4e5-f6g7-8901-bcde-f23456789012","timestamp":"2025-01-28T10:31:00.456Z","operation":"CALCULATION_ERROR","message":"Division by zero error in calculation","metadata":{"operation":"division","numerator":10,"denominator":0},"caller":{"filename":"calculations.py","line_number":123,"function_name":"divide_numbers"},"error_details":{"exception_type":"ZeroDivisionError","exception_message":"division by zero","traceback":"Traceback (most recent call last):\n  File \"calculations.py\", line 123, in divide_numbers\n    result = a / b\nZeroDivisionError: division by zero"}}
```

## Configuration

### 1. Update Django Settings

The logging configuration has been enhanced in `settings.py`:

```python
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "{levelname} | {asctime} | {name} | {filename}:{lineno} | {funcName} | {message}",
            "style": "{",
        },
        "api_format": {
            "format": "{levelname} | {asctime} | {filename}:{lineno} | {message}",
            "style": "{",
        },
    },
    "handlers": {
        "api_file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": "logs/api.log",
            "formatter": "detailed",
        },
        "error_file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": "logs/errors.log",
            "formatter": "detailed",
        },
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "api_format",
        },
    },
    "loggers": {
        "agritram": {
            "handlers": ["api_file", "console"],
            "level": "INFO",
            "propagate": True,
        },
    },
}
```

### 2. Add Middleware (Optional)

To automatically log all API requests/responses, add the enhanced logging middleware to `settings.py`:

```python
MIDDLEWARE = [
    # ... other middleware ...
    "agritram.enhanced_logging_middleware.EnhancedAPILoggingMiddleware",
    # ... rest of middleware ...
]
```

## Usage

### 1. Basic Logging

```python
from agritram.logger_utils import enhanced_log

# Simple info log with auto-generated UUID
unique_id = enhanced_log(
    message="User registration process started",
    level="INFO",
    operation_type="USER_REGISTRATION",
    metadata={"email": "<EMAIL>"}
)

# Use the same UUID for related operations
enhanced_log(
    message="Email validation completed",
    level="INFO",
    operation_type="EMAIL_VALIDATION",
    metadata={"email_valid": True},
    unique_id=unique_id  # Correlate with previous log
)
```

### 2. Error Logging with Exception Details

```python
try:
    # Some operation that might fail
    result = risky_operation()
except Exception as e:
    enhanced_log(
        message="Operation failed with unexpected error",
        level="ERROR",
        operation_type="OPERATION_ERROR",
        metadata={"operation": "risky_operation"},
        exception=e  # Automatically captures traceback
    )
```

### 3. API Call Logging

```python
from agritram.logger_utils import log_api_call

# Log external API calls
unique_id = log_api_call(
    api_name="PaymentGateway",
    endpoint="/api/v1/payments",
    method="POST",
    status_code=200,
    response_time=0.245,
    metadata={
        "payment_id": "pay_123456",
        "amount": 100.00,
        "currency": "USD"
    }
)
```

### 4. Business Operation Tracking

```python
from agritram.logger_utils import generate_api_call_uuid, enhanced_log

# Generate UUID for entire operation
operation_id = generate_api_call_uuid()

# Log each step with the same UUID
enhanced_log(
    message="Starting crop analysis",
    operation_type="CROP_ANALYSIS_START",
    metadata={"crop_type": "wheat", "field_id": "field_001"},
    unique_id=operation_id
)

enhanced_log(
    message="Analysis completed",
    operation_type="CROP_ANALYSIS_COMPLETE",
    metadata={"yield_prediction": 85.2},
    unique_id=operation_id
)
```

## Available Functions

### Core Functions

- `enhanced_log()` - Main logging function with automatic file/line detection
- `generate_api_call_uuid()` - Generate unique UUID for API calls
- `log_error_with_traceback()` - Specialized error logging with full traceback
- `log_api_call()` - Log external API calls
- `log_operation_info()` - General operation logging

### Specialized Functions

- `log_security_event_standardized()` - Security event logging
- `log_business_event()` - Business operation logging
- `log_database_operation()` - Database operation logging
- `log_performance_metric()` - Performance monitoring

## Log Levels

- **INFO**: General information, successful operations
- **WARNING**: Potential issues, security events, validation failures
- **ERROR**: Errors, exceptions, failed operations

## Metadata Guidelines

Include relevant context in metadata:

```python
metadata = {
    "user_id": 12345,
    "operation": "create_crop",
    "crop_type": "wheat",
    "field_id": "field_001",
    "processing_time_ms": 245.6,
    "validation_errors": ["invalid_date", "missing_location"]
}
```

## Log File Locations

- **API Logs**: `logs/api.log`
- **Error Logs**: `logs/errors.log`
- **Security Logs**: `logs/security.log`
- **Console**: All logs also output to console during development

## Examples

See `logging_examples.py` for comprehensive examples of all logging patterns.

## Benefits

1. **Correlation**: Track related operations across multiple log entries
2. **Debugging**: Quickly locate source of errors with file:line information
3. **Monitoring**: Structured data enables easy parsing and analysis
4. **Security**: Comprehensive audit trail for security events
5. **Performance**: Track response times and identify bottlenecks
6. **Maintenance**: Easier troubleshooting with detailed context
